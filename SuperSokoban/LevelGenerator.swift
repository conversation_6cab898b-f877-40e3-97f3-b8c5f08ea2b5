//
//  LevelGenerator.swift
//  SuperSokoban
//
//  Created by AI Assistant on 2024.
//  Copyright © 2024 SuperSokoban. All rights reserved.
//

import Foundation

/// 关卡生成器 - 负责生成200个推箱子关卡
class LevelGenerator {
    
    // MARK: - Properties
    private var generatedLevels: [[[Character]]] = []
    
    // MARK: - Initialization
    init() {
        generateAllLevels()
    }
    
    // MARK: - Public Methods
    
    /// 获取指定索引的关卡数据
    /// - Parameter index: 关卡索引 (0-199)
    /// - Returns: 关卡数据，如果索引无效则返回nil
    func getLevel(at index: Int) -> [[Character]]? {
        guard index >= 0 && index < generatedLevels.count else {
            return nil
        }
        return generatedLevels[index]
    }
    
    /// 获取总关卡数
    var numberOfLevels: Int {
        return generatedLevels.count
    }
    
    // MARK: - Private Methods
    
    /// 生成所有200个关卡
    private func generateAllLevels() {
        print("[LevelGenerator] 开始生成200个关卡...")
        
        // 生成不同难度的关卡
        generateBeginnerLevels()    // 1-50关：初学者
        generateIntermediateLevels() // 51-100关：中级
        generateAdvancedLevels()    // 101-150关：高级
        generateExpertLevels()      // 151-200关：专家级
        
        print("[LevelGenerator] 关卡生成完成，总共\(generatedLevels.count)关")
    }
    
    /// 生成初学者关卡 (1-50关)
    private func generateBeginnerLevels() {
        // 关卡1-10：基础教学关卡
        for i in 0..<10 {
            let level = generateBasicLevel(size: 5 + i/2, complexity: 1)
            generatedLevels.append(level)
        }
        
        // 关卡11-30：简单推理关卡
        for i in 10..<30 {
            let level = generateSimpleLevel(size: 6 + i/5, complexity: 2)
            generatedLevels.append(level)
        }
        
        // 关卡31-50：入门挑战关卡
        for i in 30..<50 {
            let level = generateChallengeLevel(size: 8 + i/10, complexity: 3)
            generatedLevels.append(level)
        }
    }
    
    /// 生成中级关卡 (51-100关)
    private func generateIntermediateLevels() {
        for i in 50..<100 {
            let size = 10 + (i - 50) / 8
            let complexity = 4 + (i - 50) / 25
            let level = generateIntermediateLevel(size: size, complexity: complexity)
            generatedLevels.append(level)
        }
    }
    
    /// 生成高级关卡 (101-150关)
    private func generateAdvancedLevels() {
        for i in 100..<150 {
            let size = 12 + (i - 100) / 10
            let complexity = 6 + (i - 100) / 20
            let level = generateAdvancedLevel(size: size, complexity: complexity)
            generatedLevels.append(level)
        }
    }
    
    /// 生成专家级关卡 (151-200关)
    private func generateExpertLevels() {
        for i in 150..<200 {
            let size = 15 + (i - 150) / 15
            let complexity = 8 + (i - 150) / 25
            let level = generateExpertLevel(size: size, complexity: complexity)
            generatedLevels.append(level)
        }
    }
    
    /// 生成基础关卡
    private func generateBasicLevel(size: Int, complexity: Int) -> [[Character]] {
        let actualSize = max(5, min(size, 8)) // 限制在5-8之间
        var level = Array(repeating: Array(repeating: Character("W"), count: actualSize), count: actualSize)
        
        // 创建内部空间
        for r in 1..<actualSize-1 {
            for c in 1..<actualSize-1 {
                level[r][c] = "G"
            }
        }
        
        // 放置玩家（左上角）
        level[1][1] = "P"
        
        // 放置目标点（右下角）
        level[actualSize-2][actualSize-2] = "T"
        
        // 放置箱子（中间位置）
        let boxRow = actualSize / 2
        let boxCol = actualSize / 2
        level[boxRow][boxCol] = "B"
        
        return level
    }
    
    /// 生成简单关卡
    private func generateSimpleLevel(size: Int, complexity: Int) -> [[Character]] {
        let actualSize = max(6, min(size, 10))
        var level = createEmptyLevel(size: actualSize)
        
        // 添加一些内部墙壁
        addRandomWalls(to: &level, count: complexity)
        
        // 放置游戏元素
        placeGameElements(in: &level, boxes: min(2, complexity), targets: min(2, complexity))
        
        return level
    }
    
    /// 生成挑战关卡
    private func generateChallengeLevel(size: Int, complexity: Int) -> [[Character]] {
        let actualSize = max(8, min(size, 12))
        var level = createEmptyLevel(size: actualSize)
        
        // 创建更复杂的墙壁结构
        addMazeWalls(to: &level, complexity: complexity)
        
        // 放置更多游戏元素
        placeGameElements(in: &level, boxes: min(3, complexity), targets: min(3, complexity))
        
        return level
    }
    
    /// 生成中级关卡
    private func generateIntermediateLevel(size: Int, complexity: Int) -> [[Character]] {
        let actualSize = max(10, min(size, 15))
        var level = createEmptyLevel(size: actualSize)
        
        // 创建房间结构
        addRoomStructure(to: &level, complexity: complexity)
        
        // 放置游戏元素
        placeGameElements(in: &level, boxes: min(4, complexity), targets: min(4, complexity))
        
        return level
    }
    
    /// 生成高级关卡
    private func generateAdvancedLevel(size: Int, complexity: Int) -> [[Character]] {
        let actualSize = max(12, min(size, 18))
        var level = createEmptyLevel(size: actualSize)
        
        // 创建复杂的迷宫结构
        addComplexMaze(to: &level, complexity: complexity)
        
        // 放置更多游戏元素
        placeGameElements(in: &level, boxes: min(5, complexity), targets: min(5, complexity))
        
        return level
    }
    
    /// 生成专家级关卡
    private func generateExpertLevel(size: Int, complexity: Int) -> [[Character]] {
        let actualSize = max(15, min(size, 20))
        var level = createEmptyLevel(size: actualSize)
        
        // 创建最复杂的结构
        addExpertStructure(to: &level, complexity: complexity)
        
        // 放置最多的游戏元素
        placeGameElements(in: &level, boxes: min(6, complexity), targets: min(6, complexity))
        
        return level
    }
    
    /// 创建空关卡（只有外墙）
    private func createEmptyLevel(size: Int) -> [[Character]] {
        var level = Array(repeating: Array(repeating: Character("G"), count: size), count: size)
        
        // 添加外墙
        for i in 0..<size {
            level[0][i] = "W"           // 顶墙
            level[size-1][i] = "W"      // 底墙
            level[i][0] = "W"           // 左墙
            level[i][size-1] = "W"      // 右墙
        }
        
        return level
    }
    
    /// 添加随机墙壁
    private func addRandomWalls(to level: inout [[Character]], count: Int) {
        let size = level.count
        var wallsAdded = 0
        
        while wallsAdded < count {
            let row = Int.random(in: 2..<size-2)
            let col = Int.random(in: 2..<size-2)
            
            if level[row][col] == "G" {
                level[row][col] = "W"
                wallsAdded += 1
            }
        }
    }
    
    /// 添加迷宫墙壁
    private func addMazeWalls(to level: inout [[Character]], complexity: Int) {
        let size = level.count
        
        // 添加一些垂直和水平的墙壁
        for _ in 0..<complexity {
            // 垂直墙
            let col = Int.random(in: 2..<size-2)
            let startRow = Int.random(in: 1..<size/2)
            let endRow = Int.random(in: size/2..<size-1)
            
            for row in startRow...endRow {
                if level[row][col] == "G" {
                    level[row][col] = "W"
                }
            }
            
            // 水平墙
            let row = Int.random(in: 2..<size-2)
            let startCol = Int.random(in: 1..<size/2)
            let endCol = Int.random(in: size/2..<size-1)
            
            for col in startCol...endCol {
                if level[row][col] == "G" {
                    level[row][col] = "W"
                }
            }
        }
    }
    
    /// 添加房间结构
    private func addRoomStructure(to level: inout [[Character]], complexity: Int) {
        let size = level.count
        let roomSize = size / 3
        
        // 创建几个房间
        for i in 0..<complexity {
            let roomRow = (i % 2) * roomSize + roomSize/2
            let roomCol = (i / 2) * roomSize + roomSize/2
            
            // 创建房间墙壁
            for r in max(1, roomRow-roomSize/2)...min(size-2, roomRow+roomSize/2) {
                for c in max(1, roomCol-roomSize/2)...min(size-2, roomCol+roomSize/2) {
                    if r == roomRow-roomSize/2 || r == roomRow+roomSize/2 ||
                       c == roomCol-roomSize/2 || c == roomCol+roomSize/2 {
                        if level[r][c] == "G" {
                            level[r][c] = "W"
                        }
                    }
                }
            }
            
            // 添加门
            let doorRow = roomRow + (Int.random(in: 0...1) == 0 ? -roomSize/2 : roomSize/2)
            let doorCol = roomCol + Int.random(in: -1...1)
            if doorRow > 0 && doorRow < size-1 && doorCol > 0 && doorCol < size-1 {
                level[doorRow][doorCol] = "G"
            }
        }
    }
    
    /// 添加复杂迷宫
    private func addComplexMaze(to level: inout [[Character]], complexity: Int) {
        // 实现更复杂的迷宫生成算法
        addMazeWalls(to: &level, complexity: complexity * 2)
        addRoomStructure(to: &level, complexity: complexity)
    }
    
    /// 添加专家级结构
    private func addExpertStructure(to level: inout [[Character]], complexity: Int) {
        // 组合所有结构类型
        addComplexMaze(to: &level, complexity: complexity)
        addRandomWalls(to: &level, count: complexity * 2)
    }
    
    /// 放置游戏元素（玩家、箱子、目标）
    private func placeGameElements(in level: inout [[Character]], boxes: Int, targets: Int) {
        let size = level.count
        var availablePositions: [(Int, Int)] = []
        
        // 找到所有可用位置
        for r in 1..<size-1 {
            for c in 1..<size-1 {
                if level[r][c] == "G" {
                    availablePositions.append((r, c))
                }
            }
        }
        
        guard !availablePositions.isEmpty else { return }
        
        // 放置玩家
        if let playerPos = availablePositions.randomElement() {
            level[playerPos.0][playerPos.1] = "P"
            availablePositions.removeAll { $0.0 == playerPos.0 && $0.1 == playerPos.1 }
        }
        
        // 放置目标点
        for _ in 0..<min(targets, availablePositions.count) {
            if let targetPos = availablePositions.randomElement() {
                level[targetPos.0][targetPos.1] = "T"
                availablePositions.removeAll { $0.0 == targetPos.0 && $0.1 == targetPos.1 }
            }
        }
        
        // 放置箱子
        for _ in 0..<min(boxes, availablePositions.count) {
            if let boxPos = availablePositions.randomElement() {
                level[boxPos.0][boxPos.1] = "B"
                availablePositions.removeAll { $0.0 == boxPos.0 && $0.1 == boxPos.1 }
            }
        }
    }
}
