//
//  DeadlockDetector.swift
//  SuperSokoban
//
//  Created by AI Assistant on 2024.
//  Copyright © 2024 SuperSokoban. All rights reserved.
//

import Foundation
import SpriteKit

/// 死锁检测器 - 检测游戏中的无解状态
class DeadlockDetector {
    
    // MARK: - Properties
    private weak var gameScene: GameScene?
    
    // MARK: - Initialization
    init(gameScene: GameScene) {
        self.gameScene = gameScene
    }
    
    // MARK: - Public Methods
    
    /// 检查当前游戏状态是否存在死锁
    func checkForDeadlock(boxes: [SKSpriteNode], targets: [SKSpriteNode]) -> Bool {
        guard let scene = gameScene else { return false }

        // 检查是否所有箱子都在目标位置上（胜利状态）
        let boxesOnTargets = countBoxesOnTargets(boxes: boxes, targets: targets, scene: scene)
        if boxesOnTargets == boxes.count {
            return false // 胜利状态，不是死锁
        }

        // 只有在有箱子不在目标位置时才进行死锁检测
        var deadlockedBoxes = 0

        for boxNode in boxes {
            let boxGridPos = scene.gridPosition(for: boxNode.position)

            // 如果箱子在目标位置上，跳过检查
            if isBoxOnTarget(boxPosition: boxGridPos, targets: targets, scene: scene) {
                continue
            }

            // 检查严格的角落死锁（四面被墙包围的角落）
            if isBoxInStrictCorner(at: boxGridPos, scene: scene) {
                print("[DeadlockDetector] 检测到严格角落死锁 at \(boxGridPos)")
                deadlockedBoxes += 1
            }

            // 检查明显的箱子组合死锁（多个箱子被墙完全包围）
            else if isBoxInObviousDeadlockGroup(at: boxGridPos, boxes: boxes, scene: scene) {
                print("[DeadlockDetector] 检测到明显箱子组合死锁 at \(boxGridPos)")
                deadlockedBoxes += 1
            }
        }

        // 只有当有多个箱子死锁或者死锁的箱子数量超过可用目标时才判定为死锁
        return deadlockedBoxes > 0 && deadlockedBoxes >= (boxes.count - boxesOnTargets)
    }
    
    // MARK: - Private Methods

    /// 计算在目标位置上的箱子数量
    private func countBoxesOnTargets(boxes: [SKSpriteNode], targets: [SKSpriteNode], scene: GameScene) -> Int {
        var count = 0
        for boxNode in boxes {
            let boxGridPos = scene.gridPosition(for: boxNode.position)
            if isBoxOnTarget(boxPosition: boxGridPos, targets: targets, scene: scene) {
                count += 1
            }
        }
        return count
    }

    /// 检查箱子是否在目标位置上
    private func isBoxOnTarget(boxPosition: CGPoint, targets: [SKSpriteNode], scene: GameScene) -> Bool {
        for targetNode in targets {
            let targetGridPos = scene.gridPosition(for: targetNode.position)
            if targetGridPos == boxPosition {
                return true
            }
        }
        return false
    }
    
    /// 检查箱子是否在严格的角落（三面或四面被墙包围）
    private func isBoxInStrictCorner(at position: CGPoint, scene: GameScene) -> Bool {
        let x = Int(position.x)
        let y = Int(position.y)

        // 检查四个方向是否被墙壁阻挡
        let topBlocked = scene.isWall(at: CGPoint(x: x, y: y + 1))
        let bottomBlocked = scene.isWall(at: CGPoint(x: x, y: y - 1))
        let leftBlocked = scene.isWall(at: CGPoint(x: x - 1, y: y))
        let rightBlocked = scene.isWall(at: CGPoint(x: x + 1, y: y))

        let blockedSides = [topBlocked, bottomBlocked, leftBlocked, rightBlocked].filter { $0 }.count

        // 只有当三面或四面被墙包围时才认为是严格角落死锁
        if blockedSides >= 3 {
            return true
        }

        // 或者两个相邻边被墙阻挡且没有目标在这个角落
        return (topBlocked && leftBlocked) ||
               (topBlocked && rightBlocked) ||
               (bottomBlocked && leftBlocked) ||
               (bottomBlocked && rightBlocked)
    }

    /// 检查箱子是否在角落（无法移动的位置）- 保留原方法用于兼容
    private func isBoxInCorner(at position: CGPoint, scene: GameScene) -> Bool {
        let x = Int(position.x)
        let y = Int(position.y)

        // 检查四个方向是否被墙壁阻挡
        let topBlocked = scene.isWall(at: CGPoint(x: x, y: y + 1))
        let bottomBlocked = scene.isWall(at: CGPoint(x: x, y: y - 1))
        let leftBlocked = scene.isWall(at: CGPoint(x: x - 1, y: y))
        let rightBlocked = scene.isWall(at: CGPoint(x: x + 1, y: y))

        // 如果箱子的两个相邻边都被墙壁阻挡，则形成角落死锁
        return (topBlocked && leftBlocked) ||
               (topBlocked && rightBlocked) ||
               (bottomBlocked && leftBlocked) ||
               (bottomBlocked && rightBlocked)
    }
    
    /// 检查箱子是否形成明显的死锁组合（更严格的检测）
    private func isBoxInObviousDeadlockGroup(at position: CGPoint, boxes: [SKSpriteNode], scene: GameScene) -> Bool {
        let x = Int(position.x)
        let y = Int(position.y)

        // 检查相邻位置是否有其他箱子
        let hasBoxAbove = hasBox(at: CGPoint(x: x, y: y + 1), boxes: boxes, scene: scene)
        let hasBoxBelow = hasBox(at: CGPoint(x: x, y: y - 1), boxes: boxes, scene: scene)
        let hasBoxLeft = hasBox(at: CGPoint(x: x - 1, y: y), boxes: boxes, scene: scene)
        let hasBoxRight = hasBox(at: CGPoint(x: x + 1, y: y), boxes: boxes, scene: scene)

        // 只有当箱子被其他箱子和墙完全包围时才认为是明显死锁
        let adjacentBoxes = [hasBoxAbove, hasBoxBelow, hasBoxLeft, hasBoxRight].filter { $0 }.count

        // 如果被三个或更多箱子包围，很可能是死锁
        if adjacentBoxes >= 3 {
            return true
        }

        // 检查是否形成2x2的箱子方块（经典死锁模式）
        if hasBoxAbove && hasBoxRight {
            let hasBoxDiagonal = hasBox(at: CGPoint(x: x + 1, y: y + 1), boxes: boxes, scene: scene)
            if hasBoxDiagonal {
                return true
            }
        }

        return false
    }

    /// 检查箱子是否与其他箱子形成死锁组合 - 保留原方法用于兼容
    private func isBoxInDeadlockGroup(at position: CGPoint, boxes: [SKSpriteNode], scene: GameScene) -> Bool {
        let x = Int(position.x)
        let y = Int(position.y)

        // 检查相邻位置是否有其他箱子
        let hasBoxAbove = hasBox(at: CGPoint(x: x, y: y + 1), boxes: boxes, scene: scene)
        let hasBoxBelow = hasBox(at: CGPoint(x: x, y: y - 1), boxes: boxes, scene: scene)
        let hasBoxLeft = hasBox(at: CGPoint(x: x - 1, y: y), boxes: boxes, scene: scene)
        let hasBoxRight = hasBox(at: CGPoint(x: x + 1, y: y), boxes: boxes, scene: scene)

        // 检查垂直方向的死锁
        if hasBoxAbove && hasBoxBelow {
            let wallLeft = scene.isWall(at: CGPoint(x: x - 1, y: y))
            let wallRight = scene.isWall(at: CGPoint(x: x + 1, y: y))
            if wallLeft || wallRight {
                return true
            }
        }

        // 检查水平方向的死锁
        if hasBoxLeft && hasBoxRight {
            let wallTop = scene.isWall(at: CGPoint(x: x, y: y + 1))
            let wallBottom = scene.isWall(at: CGPoint(x: x, y: y - 1))
            if wallTop || wallBottom {
                return true
            }
        }

        return false
    }
    
    /// 检查箱子是否形成墙边死锁
    private func isBoxAgainstWallDeadlock(at position: CGPoint, boxes: [SKSpriteNode], targets: [SKSpriteNode], scene: GameScene) -> Bool {
        let x = Int(position.x)
        let y = Int(position.y)
        
        // 检查是否靠墙
        let againstTopWall = scene.isWall(at: CGPoint(x: x, y: y + 1))
        let againstBottomWall = scene.isWall(at: CGPoint(x: x, y: y - 1))
        let againstLeftWall = scene.isWall(at: CGPoint(x: x - 1, y: y))
        let againstRightWall = scene.isWall(at: CGPoint(x: x + 1, y: y))
        
        // 如果箱子靠墙，检查沿墙方向是否有目标
        if againstTopWall || againstBottomWall {
            // 检查水平方向是否有可达的目标
            return !hasReachableTargetInDirection(from: position, horizontal: true, targets: targets, scene: scene)
        }
        
        if againstLeftWall || againstRightWall {
            // 检查垂直方向是否有可达的目标
            return !hasReachableTargetInDirection(from: position, horizontal: false, targets: targets, scene: scene)
        }
        
        return false
    }
    
    /// 检查指定位置是否有箱子
    private func hasBox(at position: CGPoint, boxes: [SKSpriteNode], scene: GameScene) -> Bool {
        for boxNode in boxes {
            let boxGridPos = scene.gridPosition(for: boxNode.position)
            if boxGridPos == position {
                return true
            }
        }
        return false
    }
    
    /// 检查在指定方向是否有可达的目标
    private func hasReachableTargetInDirection(from position: CGPoint, horizontal: Bool, targets: [SKSpriteNode], scene: GameScene) -> Bool {
        for targetNode in targets {
            let targetGridPos = scene.gridPosition(for: targetNode.position)
            
            if horizontal {
                // 检查水平方向
                if targetGridPos.y == position.y {
                    return true
                }
            } else {
                // 检查垂直方向
                if targetGridPos.x == position.x {
                    return true
                }
            }
        }
        return false
    }
}
