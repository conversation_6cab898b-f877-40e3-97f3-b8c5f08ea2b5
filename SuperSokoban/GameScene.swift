import SpriteKit
import GameplayKit
import AudioToolbox

class GameScene: SKScene {

    // MARK: - Properties
    private var lastUpdateTime : TimeInterval = 0

    // 管理器实例
    private var gameStateManager: GameStateManager!
    private var deadlockDetector: DeadlockDetector!
    private var uiManager: UIManager!
    private var audioManager: AudioManager!
    private var initialTouch: CGPoint = .zero
    private var isMoving: Bool = false // 防止连续快速滑动导致多次移动

    // 游戏元素节点
    private var player: SKSpriteNode?
    private var boxes: [SKSpriteNode] = []
    private var walls: [SKSpriteNode] = []
    private var targets: [SKSpriteNode] = []
    private var groundTiles: [SKSpriteNode] = []

    private var levelManager = LevelManager()
    private var levelGenerator = LevelGenerator()
    private var currentLevelIndex = 0
    private var levelLabel: SKLabelNode? // 用于显示当前关卡
    private var resetButton: SKLabelNode? // 重置按钮
    // Level selection removed for cleaner UI
    private var levelData: [[Character]] = [] // 将由LevelManager提供

    // 游戏状态保存
    private var initialPlayerPosition: CGPoint = .zero
    private var initialBoxPositions: [CGPoint] = []
    private var moveHistory: [(playerPos: CGPoint, boxPositions: [CGPoint])] = []

    // 安全区域
    var safeAreaInsets: UIEdgeInsets = .zero

    // 瓦片大小 - 根据屏幕大小和关卡动态调整
    private var tileSize: CGFloat = 64.0

    // 移动路径显示
    private var pathNodes: [SKSpriteNode] = []
    
    override init(size: CGSize) {
        super.init(size: size)
        print("[GameScene] Initialized with size: \(size)") // 调试信息：场景初始化大小

        // 根据屏幕大小动态调整瓦片大小
        let minDimension = min(size.width, size.height)
        tileSize = max(48.0, min(80.0, minDimension / 8.0))
    }
    
    required init?(coder aDecoder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    override func sceneDidLoad() {
        // 初始化管理器
        gameStateManager = GameStateManager()
        deadlockDetector = DeadlockDetector(gameScene: self)
        uiManager = UIManager(gameScene: self)
        audioManager = AudioManager(gameScene: self)

        // 设置渐变背景
        setupGradientBackground()
        print("[GameScene] sceneDidLoad called. Scene size: \(self.size)")
        self.lastUpdateTime = 0

        // 预加载音效
        audioManager.preloadSounds()

        // 加载第一个关卡
        loadLevel(index: currentLevelIndex)

        print("[GameScene] 游戏场景初始化完成")
        print(audioManager.getAudioStatusReport())
    }

    /// 设置统一的渐变背景
    private func setupGradientBackground() {
        // 移除旧的背景
        self.children.filter { $0.name == "gameBackground" }.forEach { $0.removeFromParent() }

        // 创建真正的渐变背景
        let gradientTexture = createGradientTexture()
        let backgroundNode = SKSpriteNode(texture: gradientTexture)
        backgroundNode.position = CGPoint(x: self.size.width/2, y: self.size.height/2)
        backgroundNode.zPosition = -100
        backgroundNode.size = self.size
        backgroundNode.name = "gameBackground"
        self.addChild(backgroundNode)

        print("[GameScene] 统一渐变背景已设置")
    }

    /// 创建渐变纹理
    private func createGradientTexture() -> SKTexture {
        let size = CGSize(width: 32, height: 32) // 小纹理，会被拉伸
        let renderer = UIGraphicsImageRenderer(size: size)

        let image = renderer.image { context in
            let cgContext = context.cgContext

            // 创建渐变
            let colorSpace = CGColorSpaceCreateDeviceRGB()
            let colors = [
                UIColor(red: 0.2, green: 0.3, blue: 0.5, alpha: 1.0).cgColor,  // 深蓝色
                UIColor(red: 0.4, green: 0.5, blue: 0.7, alpha: 1.0).cgColor,  // 中蓝色
                UIColor(red: 0.6, green: 0.7, blue: 0.9, alpha: 1.0).cgColor   // 浅蓝色
            ] as CFArray

            let gradient = CGGradient(colorsSpace: colorSpace, colors: colors, locations: [0.0, 0.5, 1.0])!

            // 绘制渐变
            cgContext.drawLinearGradient(
                gradient,
                start: CGPoint(x: 0, y: size.height),
                end: CGPoint(x: 0, y: 0),
                options: []
            )
        }

        return SKTexture(image: image)
    }
    
    /// 加载指定索引的关卡
    /// - Parameter index: 关卡索引
    func loadLevel(index: Int) {
        print("[GameScene] Attempting to load level at index: \(index)") // 调试信息：尝试加载关卡

        // 优先从LevelGenerator获取关卡数据（支持200关）
        var newLevelData: [[Character]]?

        if index < levelGenerator.numberOfLevels {
            newLevelData = levelGenerator.getLevel(at: index)
            print("[GameScene] Loading from LevelGenerator: level \(index + 1)/\(levelGenerator.numberOfLevels)")
        } else {
            // 回退到原始的5关
            let fallbackIndex = index % levelManager.numberOfLevels
            newLevelData = levelManager.getLevel(at: fallbackIndex)
            print("[GameScene] Loading from LevelManager: level \(fallbackIndex + 1)/\(levelManager.numberOfLevels)")
        }

        if let levelData = newLevelData {
            self.levelData = levelData
            currentLevelIndex = index

            // 根据关卡调整瓦片大小
            adjustTileSizeForLevel(index: index)

            print("[GameScene] Level data loaded successfully for index \(index)") // 调试信息：关卡数据加载成功
            setupLevel() // 使用新的关卡数据设置场景

            // 保存初始状态到新的管理器
            saveInitialStateToManager()
        } else {
            print("无法加载关卡索引: \(index)。可能是最后一个关卡了。")
            // 可以选择显示游戏结束画面或循环到第一关
            // loadLevel(index: 0) // 回到第一关
            showGameCompletedScreen()
        }
    }

    /// 根据关卡调整瓦片大小
    /// - Parameter index: 关卡索引
    private func adjustTileSizeForLevel(index: Int) {
        let levelWidth = levelData.first?.count ?? 5
        let levelHeight = levelData.count

        // 计算合适的瓦片大小
        let availableWidth = self.size.width - 40 // 留出边距
        let availableHeight = self.size.height - safeAreaInsets.top - safeAreaInsets.bottom - 120 // 留出UI空间

        let maxTileWidth = availableWidth / CGFloat(levelWidth)
        let maxTileHeight = availableHeight / CGFloat(levelHeight)

        tileSize = min(maxTileWidth, maxTileHeight, 80) // 最大不超过80
        tileSize = max(tileSize, 40) // 最小不小于40

        print("[GameScene] Adjusted tile size to \(tileSize) for level \(index + 1)")
    }

    /// 保存初始状态到新的管理器
    private func saveInitialStateToManager() {
        // 确保玩家和箱子已经准备好
        guard let playerNode = self.player, !boxes.isEmpty else {
            print("[GameScene] ERROR: Cannot save initial state - player or boxes not ready")
            return
        }

        let playerGridPos = gridPosition(for: playerNode.position)
        let boxGridPositions = boxes.map { self.gridPosition(for: $0.position) }

        gameStateManager.saveInitialState(playerPosition: playerGridPos, boxPositions: boxGridPositions)

        print("[GameScene] Initial state saved to manager - Player: \(playerGridPos), Boxes: \(boxGridPositions)")
        print("[GameScene] Number of boxes saved: \(boxGridPositions.count)")
    }

    /// 保存初始状态（旧版本，保留用于兼容性）
    private func saveInitialState() {
        // CRITICAL FIX: Ensure we have valid player and boxes before saving
        guard let playerNode = self.player, !boxes.isEmpty else {
            print("[GameScene] ERROR: Cannot save initial state - player or boxes not ready")
            return
        }

        initialPlayerPosition = playerNode.position
        initialBoxPositions = boxes.map { $0.position }

        print("[GameScene] Initial state saved - Player: \(initialPlayerPosition), Boxes: \(initialBoxPositions)")
        print("[GameScene] Number of boxes saved: \(initialBoxPositions.count)")
    }

    /// 显示游戏通关画面（占位）
    func showGameCompletedScreen() {
        let completedLabel = SKLabelNode(fontNamed: "Chalkduster")
        completedLabel.text = "所有关卡完成!"
        completedLabel.fontSize = 40
        completedLabel.fontColor = SKColor.blue
        completedLabel.position = CGPoint(x: self.size.width/2, y: self.size.height/2)
        completedLabel.zPosition = 100
        self.addChild(completedLabel)
        // 可以在这里添加返回主菜单或重置游戏的按钮
    }
    
    /// 设置关卡
    /// 根据 levelData 初始化游戏场景中的各个元素
    func setupLevel() {
        print("[GameScene] Setting up level with current data...") // 调试信息：开始设置关卡
        // 清理旧的节点（如果有关卡重载功能）
        self.removeAllChildren() // 清除所有旧节点，包括UI元素
        boxes.removeAll()
        walls.removeAll()
        targets.removeAll()
        groundTiles.removeAll()
        player = nil
        levelLabel = nil // 清除旧的标签引用
        resetButton = nil // 清除旧的按钮引用

        // 重新设置统一的渐变背景
        setupGradientBackground()

        // 使用UIManager设置所有UI元素
        uiManager.setupAllUIElements(safeAreaInsets: safeAreaInsets, currentLevel: currentLevelIndex)

        let levelHeight = levelData.count
        let levelWidth = levelData.first?.count ?? 0

        // 计算场景的中心偏移量，使关卡居中
        let sceneWidth = CGFloat(levelWidth) * tileSize
        let sceneHeight = CGFloat(levelHeight) * tileSize
        let offsetX = (self.size.width - sceneWidth) / 2.0
        let offsetY = (self.size.height - sceneHeight) / 2.0
        print("[GameScene] Level dimensions: \(levelWidth)x\(levelHeight), TileSize: \(tileSize)") // 调试信息：关卡维度和瓦片大小
        print("[GameScene] Calculated scene size: \(sceneWidth)x\(sceneHeight), Offset: (\(offsetX), \(offsetY))") // 调试信息：计算出的场景大小和偏移

        for r in 0..<levelHeight {
            for c in 0..<levelWidth {
                let char = levelData[r][c]
                let position = CGPoint(x: CGFloat(c) * tileSize + tileSize / 2.0 + offsetX,
                                       y: self.size.height - (CGFloat(r) * tileSize + tileSize / 2.0) - offsetY) // Y轴反转，因为SpriteKit原点在左下角
                
                // 始终先添加地面 - 使用更柔和的颜色
                let groundNode = createGroundTile()
                groundNode.position = position
                groundNode.zPosition = 0 // 地面在最底层
                self.addChild(groundNode)
                groundTiles.append(groundNode)

                switch char {
                case "W": // 墙
                    let wallNode = createWallTile()
                    wallNode.position = position
                    self.addChild(wallNode)
                    walls.append(wallNode)
                case "P": // 玩家
                    let playerNode = createPlayerTile()
                    playerNode.position = position
                    self.addChild(playerNode)
                    self.player = playerNode
                case "B": // 箱子
                    let boxNode = createBoxTile()
                    boxNode.position = position
                    self.addChild(boxNode)
                    boxes.append(boxNode)
                case "T": // 目标点
                    let targetNode = createTargetTile()
                    targetNode.position = position
                    self.addChild(targetNode)
                    targets.append(targetNode)
                case "G": // 空地 (已经由默认地面处理)
                    break
                default:
                    print("未知的关卡字符: \(char)")
                }
            }
        }
        print("[GameScene] setupLevel completed. Scene size: \(self.size), Children count: \(self.children.count)") // 调试信息：setupLevel完成后的场景大小和子节点数量
    }

    override func touchesBegan(_ touches: Set<UITouch>, with event: UIEvent?) {
        guard let touch = touches.first else { return }
        let location = touch.location(in: self)

        // 检查关卡选择菜单相关点击
        let touchedNode = self.atPoint(location)

        // Handle deadlock alert button clicks
        if let nodeName = touchedNode.name {
            if nodeName == "deadlockRetry" || nodeName == "deadlockRetryLabel" {
                closeDeadlockAlert()
                resetLevel()
                return

            } else if nodeName == "deadlockOverlay" {
                closeDeadlockAlert()
                return
            }
        }

        // 检查按钮点击
        if let buttonType = uiManager.handleButtonTouch(at: location) {
            uiManager.animateButtonPress(buttonType: buttonType) {
                switch buttonType {
                case .retry:
                    self.audioManager.playSound(.reset)
                    self.resetLevel()
                }
            }
            return
        }

        // Level selection button removed for cleaner UI

        // 如果没有点击按钮且不在移动中，则处理点击移动
        if !isMoving {
            initialTouch = location
            handleTouchMovement(to: location)
        }
    }
    
    override func touchesMoved(_ touches: Set<UITouch>, with event: UIEvent?) {
        // 如果需要拖拽类型的控制，可以在这里实现
    }
    
    override func touchesEnded(_ touches: Set<UITouch>, with event: UIEvent?) {
        guard let touch = touches.first, let _ = self.player, !isMoving else { return }
        let finalTouch = touch.location(in: self)
        
        let dx = finalTouch.x - initialTouch.x
        let dy = finalTouch.y - initialTouch.y
        
        // 判断滑动方向
        if abs(dx) > abs(dy) { // 水平滑动
            if abs(dx) > tileSize / 2 { // 确保滑动距离足够
                if dx > 0 {
                    movePlayer(direction: CGVector(dx: 1, dy: 0)) // .right
                } else {
                    movePlayer(direction: CGVector(dx: -1, dy: 0)) // .left
                }
            }
        } else { // 垂直滑动
            if abs(dy) > tileSize / 2 { // 确保滑动距离足够
                if dy > 0 {
                    movePlayer(direction: CGVector(dx: 0, dy: 1)) // .up
                } else {
                    movePlayer(direction: CGVector(dx: 0, dy: -1)) // .down
                }
            }
        }
        initialTouch = .zero // 重置
    }
    
    /// 移动玩家
    /// - Parameter direction: 移动方向 (up, down, left, right)
    func movePlayer(direction: CGVector) {
        guard let playerNode = self.player, !isMoving else { return }
        
        isMoving = true // 开始移动
        
        let currentGridPosition = gridPosition(for: playerNode.position)
        let targetGridPosition = CGPoint(x: currentGridPosition.x + direction.dx,
                                         y: currentGridPosition.y + direction.dy)
        
        // 检查目标位置是否为墙
        if isWall(at: targetGridPosition) {
            print("撞墙了!")
            isMoving = false // 撞墙，移动结束
            return
        }
        
        // 检查目标位置是否有箱子
        if let boxToPush = box(at: targetGridPosition) {
            let nextGridPositionForBox = CGPoint(x: targetGridPosition.x + direction.dx,
                                                 y: targetGridPosition.y + direction.dy)
            
            // 检查箱子前方是否是墙或者另一个箱子
            if isWall(at: nextGridPositionForBox) || box(at: nextGridPositionForBox) != nil {
                print("箱子推不动!")
                isMoving = false
                return
            }
            
            // 移动箱子
            let boxTargetScenePosition = scenePosition(for: nextGridPositionForBox)
            let moveBoxAction = SKAction.move(to: boxTargetScenePosition, duration: 0.2)
            // 给箱子移动添加一点视觉反馈，比如轻微的缩放
            let scaleUp = SKAction.scale(to: 1.1, duration: 0.1)
            let scaleDown = SKAction.scale(to: 1.0, duration: 0.1)
            let moveAndScale = SKAction.sequence([scaleUp, moveBoxAction, scaleDown])
            
            boxToPush.run(moveAndScale) {
                // 播放推箱子音效
                self.audioManager.playSound(.push)

                // 推箱子后检查死锁状态
                if self.deadlockDetector.checkForDeadlock(boxes: self.boxes, targets: self.targets) {
                    self.showDeadlockAlert()
                }
            }
            
            // 更新箱子在levelData中的位置（如果需要动态更新地图数据）
            // self.updateLevelDataForBoxMove(from: targetGridPosition, to: nextGridPositionForBox)
        }
        
        let targetScenePosition = scenePosition(for: targetGridPosition)
        let movePlayerAction = SKAction.move(to: targetScenePosition, duration: 0.2)
        // 给玩家移动添加一点视觉反馈
        let wiggleRight = SKAction.rotate(byAngle: .pi / 16, duration: 0.05)
        let wiggleLeft = SKAction.rotate(byAngle: -.pi / 16, duration: 0.05)
        let straighten = SKAction.rotate(toAngle: 0, duration: 0.05)
        let wiggleSequence = SKAction.sequence([wiggleRight, wiggleLeft, wiggleRight, wiggleLeft, straighten])
        
        let groupAction = SKAction.group([movePlayerAction, wiggleSequence])
        


        playerNode.run(groupAction) {
            self.isMoving = false // 移动完成
            self.checkWinCondition() // 检查胜利条件
        }
    }

    // 移动历史保存函数已移至GameStateManager



    // 死锁检测函数已移至DeadlockDetector

    /// 显示死锁警告对话框
    private func showDeadlockAlert() {
        // 播放失败音效序列
        audioManager.playFailureSequence()

        // 创建半透明背景
        let overlay = SKSpriteNode(color: SKColor.black.withAlphaComponent(0.8), size: self.size)
        overlay.position = CGPoint(x: self.size.width/2, y: self.size.height/2)
        overlay.zPosition = 300
        overlay.name = "deadlockOverlay"
        self.addChild(overlay)

        // 创建警告框背景
        let alertBackground = SKShapeNode(rectOf: CGSize(width: 280, height: 180), cornerRadius: 15)
        alertBackground.fillColor = SKColor.white
        alertBackground.strokeColor = SKColor.red
        alertBackground.lineWidth = 3
        alertBackground.position = CGPoint(x: self.size.width/2, y: self.size.height/2)
        alertBackground.zPosition = 301
        alertBackground.name = "deadlockAlert"
        self.addChild(alertBackground)

        // 标题
        let titleLabel = SKLabelNode(fontNamed: "HelveticaNeue-Bold")
        titleLabel.text = "死锁检测"
        titleLabel.fontSize = 22
        titleLabel.fontColor = SKColor.red
        titleLabel.position = CGPoint(x: self.size.width/2, y: self.size.height/2 + 40)
        titleLabel.zPosition = 302
        titleLabel.name = "deadlockTitle"
        self.addChild(titleLabel)

        // 消息
        let messageLabel = SKLabelNode(fontNamed: "HelveticaNeue")
        messageLabel.text = "箱子被推到无法移动的位置！"
        messageLabel.fontSize = 16
        messageLabel.fontColor = SKColor.black
        messageLabel.position = CGPoint(x: self.size.width/2, y: self.size.height/2 + 5)
        messageLabel.zPosition = 302
        messageLabel.name = "deadlockMessage"
        self.addChild(messageLabel)

        // 重试按钮
        let retryButton = SKShapeNode(rectOf: CGSize(width: 100, height: 35), cornerRadius: 8)
        retryButton.fillColor = SKColor.orange
        retryButton.strokeColor = SKColor.black
        retryButton.lineWidth = 2
        retryButton.position = CGPoint(x: self.size.width/2 - 60, y: self.size.height/2 - 40)
        retryButton.zPosition = 302
        retryButton.name = "deadlockRetry"
        self.addChild(retryButton)

        let retryLabel = SKLabelNode(fontNamed: "HelveticaNeue-Bold")
        retryLabel.text = "重试"
        retryLabel.fontSize = 16
        retryLabel.fontColor = SKColor.white
        retryLabel.position = CGPoint(x: self.size.width/2 - 60, y: self.size.height/2 - 48)
        retryLabel.zPosition = 303
        retryLabel.name = "deadlockRetryLabel"
        self.addChild(retryLabel)


    }

    /// 关闭死锁警告对话框
    private func closeDeadlockAlert() {
        self.children.filter { node in
            node.name?.contains("deadlock") == true
        }.forEach { $0.removeFromParent() }
    }

    /// PIXEL-PERFECT FIX: Convert scene coordinates to grid coordinates with precise alignment
    /// - Parameter scenePosition: SpriteKit scene CGPoint coordinates
    /// - Returns: Corresponding grid coordinates (column, row)
    func gridPosition(for scenePosition: CGPoint) -> CGPoint {
        let levelHeight = levelData.count
        let levelWidth = levelData.first?.count ?? 0
        let sceneWidth = CGFloat(levelWidth) * tileSize
        let sceneHeight = CGFloat(levelHeight) * tileSize

        // PIXEL-PERFECT: Use precise centering calculation with proper rounding
        let offsetX = (self.size.width - sceneWidth) / 2.0
        let offsetY = (self.size.height - sceneHeight) / 2.0

        // Calculate grid position with precise alignment
        let adjustedX = scenePosition.x - offsetX - tileSize / 2.0
        let adjustedY = self.size.height - scenePosition.y - offsetY - tileSize / 2.0

        // Use round() for more accurate grid position calculation
        let gridX = round(adjustedX / tileSize)
        let gridY = round(adjustedY / tileSize)

        return CGPoint(x: gridX, y: gridY)
    }

    /// PIXEL-PERFECT FIX: Convert grid coordinates to scene coordinates with precise centering
    /// - Parameter gridPosition: Grid coordinates (column, row)
    /// - Returns: Corresponding SpriteKit scene CGPoint coordinates
    func scenePosition(for gridPosition: CGPoint) -> CGPoint {
        let levelHeight = levelData.count
        let levelWidth = levelData.first?.count ?? 0
        let sceneWidth = CGFloat(levelWidth) * tileSize
        let sceneHeight = CGFloat(levelHeight) * tileSize

        // PIXEL-PERFECT: Use precise centering calculation
        let offsetX = (self.size.width - sceneWidth) / 2.0
        let offsetY = (self.size.height - sceneHeight) / 2.0

        // PIXEL-PERFECT: Center elements precisely within their grid cells with proper rounding
        let x = round(CGFloat(gridPosition.x) * tileSize + tileSize / 2.0 + offsetX)
        let y = round(self.size.height - (CGFloat(gridPosition.y) * tileSize + tileSize / 2.0) - offsetY)

        return CGPoint(x: x, y: y)
    }

    /// 检查指定网格位置是否是墙
    /// - Parameter gridPosition: 网格坐标 (列, 行)
    /// - Returns: 如果是墙则返回true，否则返回false
    /// 根据网格坐标获取箱子节点
    /// - Parameter gridPosition: 网格坐标
    /// - Returns: 如果该位置有箱子，则返回对应的SKSpriteNode，否则返回nil
    func box(at gridPosition: CGPoint) -> SKSpriteNode? {
        for boxNode in boxes {
            if self.gridPosition(for: boxNode.position) == gridPosition {
                return boxNode
            }
        }
        return nil
    }

    /// 检查指定网格位置是否是墙
    /// - Parameter gridPosition: 网格坐标 (列, 行)
    /// - Returns: 如果是墙则返回true，否则返回false
    func isWall(at gridPosition: CGPoint) -> Bool {
        let r = Int(gridPosition.y)
        let c = Int(gridPosition.x)

        // 边界检查
        guard r >= 0 && r < levelData.count && c >= 0 && c < (levelData.first?.count ?? 0) else {
            return true // 越界视为墙
        }
        return levelData[r][c] == "W"
    }

    /// 检查指定网格位置是否是目标位置（用于死锁检测）
    /// - Parameter gridPosition: 网格坐标 (列, 行)
    /// - Returns: 如果是目标位置则返回true，否则返回false
    func isTargetPosition(at gridPosition: CGPoint) -> Bool {
        // 检查是否有目标节点在这个位置
        for targetNode in targets {
            let targetGridPos = self.gridPosition(for: targetNode.position)
            if targetGridPos == gridPosition {
                return true
            }
        }
        return false
    }
    

    

    
    override func touchesCancelled(_ touches: Set<UITouch>, with event: UIEvent?) {
        // 触摸取消逻辑
    }
    
    
    override func update(_ currentTime: TimeInterval) {
        // Called before each frame is rendered
        
        // Initialize _lastUpdateTime if it has not been set yet
        if (self.lastUpdateTime == 0) {
            self.lastUpdateTime = currentTime
        }
        
        // Calculate time since last update
        let _ = currentTime - self.lastUpdateTime
        
        // 在这里更新游戏状态
        
        self.lastUpdateTime = currentTime
    }
    
    /// 检查胜利条件：所有箱子是否都在目标点上
    func checkWinCondition() {
        var allBoxesOnTargets = true
        if targets.isEmpty { // 如果没有目标点，则不判断胜利 (例如某些特殊关卡)
            return
        }
        if boxes.count != targets.count { // 箱子和目标点数量不一致，肯定无法胜利
            // 这个判断可能需要根据实际游戏逻辑调整，例如允许更多目标点
            // allBoxesOnTargets = false
            print("警告: 箱子数量 (\(boxes.count)) 与目标点数量 (\(targets.count)) 不匹配。")
            // return // 如果严格要求数量匹配，则直接返回
        }

        for boxNode in boxes {
            let boxGridPos = gridPosition(for: boxNode.position)
            var isOnTarget = false
            for targetNode in targets {
                if gridPosition(for: targetNode.position) == boxGridPos {
                    isOnTarget = true
                    // 可以给箱子或目标点一个视觉反馈，比如变色
                    // boxNode.color = .yellow // 示例：箱子在目标点上变黄
                    break
                }
            }
            if !isOnTarget {
                allBoxesOnTargets = false
                break
            }
        }
        
        if allBoxesOnTargets {
            print("恭喜！通过本关！")
            // TODO: 实现过关后的操作，例如加载下一关，显示胜利界面等
            // 示例：简单地重新加载当前关卡
            // setupLevel()
            
            // 播放通关动画和音效
            playWinAnimationAndSound()
            
            // 延迟一段时间后加载下一关或返回菜单
            let waitAction = SKAction.wait(forDuration: 2.0) // 等待2秒
            let nextLevelAction = SKAction.run { [weak self] in
                guard let self = self else { return }
                self.loadLevel(index: self.currentLevelIndex + 1)
            }
            self.run(SKAction.sequence([waitAction, nextLevelAction]))
        }
    }
    
    /// VICTORY ANIMATION ENHANCEMENT: Professional victory animation with multiple effects
    func playWinAnimationAndSound() {
        // Create semi-transparent overlay
        let overlay = SKSpriteNode(color: SKColor.black.withAlphaComponent(0.6), size: self.size)
        overlay.position = CGPoint(x: self.size.width/2, y: self.size.height/2)
        overlay.zPosition = 150
        overlay.name = "victoryOverlay"
        self.addChild(overlay)

        // Create victory background panel
        let victoryPanel = SKShapeNode(rectOf: CGSize(width: 300, height: 200), cornerRadius: 20)
        victoryPanel.fillColor = SKColor.white.withAlphaComponent(0.95)
        victoryPanel.strokeColor = SKColor(red: 1.0, green: 0.84, blue: 0.0, alpha: 1.0) // Gold color
        victoryPanel.lineWidth = 4
        victoryPanel.position = CGPoint(x: self.size.width/2, y: self.size.height/2)
        victoryPanel.zPosition = 151
        victoryPanel.name = "victoryPanel"
        self.addChild(victoryPanel)

        // Main victory label with enhanced styling
        let winLabel = SKLabelNode(fontNamed: "HelveticaNeue-Bold")
        winLabel.text = "🎉 胜利! 🎉"
        winLabel.fontSize = 36
        winLabel.fontColor = SKColor(red: 0.0, green: 0.7, blue: 0.0, alpha: 1.0)
        winLabel.position = CGPoint(x: self.size.width/2, y: self.size.height/2 + 30)
        winLabel.zPosition = 152
        winLabel.name = "victoryLabel"
        self.addChild(winLabel)

        // Level completion message
        let levelLabel = SKLabelNode(fontNamed: "HelveticaNeue")
        levelLabel.text = "关卡 \(currentLevelIndex + 1) 完成!"
        levelLabel.fontSize = 20
        levelLabel.fontColor = SKColor.darkGray
        levelLabel.position = CGPoint(x: self.size.width/2, y: self.size.height/2 - 10)
        levelLabel.zPosition = 152
        levelLabel.name = "levelCompleteLabel"
        self.addChild(levelLabel)

        // Next level preview - 支持200关
        let nextLevelLabel = SKLabelNode(fontNamed: "HelveticaNeue")
        let totalLevels = levelGenerator.numberOfLevels
        if currentLevelIndex + 1 < totalLevels {
            nextLevelLabel.text = "准备进入关卡 \(currentLevelIndex + 2)/\(totalLevels)..."
        } else {
            nextLevelLabel.text = "恭喜完成所有\(totalLevels)关!"
        }
        nextLevelLabel.fontSize = 16
        nextLevelLabel.fontColor = SKColor.blue
        nextLevelLabel.position = CGPoint(x: self.size.width/2, y: self.size.height/2 - 40)
        nextLevelLabel.zPosition = 152
        nextLevelLabel.name = "nextLevelLabel"
        self.addChild(nextLevelLabel)

        // Enhanced animations
        // Panel entrance animation
        victoryPanel.setScale(0)
        let panelScaleUp = SKAction.scale(to: 1.0, duration: 0.4)
        panelScaleUp.timingMode = .easeOut
        victoryPanel.run(panelScaleUp)

        // Label entrance animation with bounce
        winLabel.setScale(0)
        let labelScaleUp = SKAction.scale(to: 1.2, duration: 0.3)
        let labelScaleDown = SKAction.scale(to: 1.0, duration: 0.2)
        labelScaleUp.timingMode = .easeOut
        labelScaleDown.timingMode = .easeIn
        let bounceSequence = SKAction.sequence([labelScaleUp, labelScaleDown])
        winLabel.run(SKAction.sequence([SKAction.wait(forDuration: 0.2), bounceSequence]))

        // Color cycling animation for victory label
        let colorCycle = SKAction.sequence([
            SKAction.colorize(with: SKColor.green, colorBlendFactor: 1.0, duration: 0.5),
            SKAction.colorize(with: SKColor(red: 1.0, green: 0.84, blue: 0.0, alpha: 1.0), colorBlendFactor: 1.0, duration: 0.5),
            SKAction.colorize(with: SKColor.blue, colorBlendFactor: 1.0, duration: 0.5),
            SKAction.colorize(with: SKColor.green, colorBlendFactor: 1.0, duration: 0.5)
        ])
        winLabel.run(SKAction.repeatForever(colorCycle))

        // Sparkle effects around the victory panel
        createSparkleEffects(around: victoryPanel.position)

        // Play enhanced victory sound sequence
        audioManager.playVictorySequence()

        // Auto-remove after delay
        let waitAction = SKAction.wait(forDuration: 3.0)
        let fadeOut = SKAction.fadeOut(withDuration: 0.5)
        let removeAction = SKAction.removeFromParent()
        let cleanupAction = SKAction.run { [weak self] in
            self?.removeVictoryElements()
        }

        overlay.run(SKAction.sequence([waitAction, fadeOut, removeAction]))
        victoryPanel.run(SKAction.sequence([waitAction, fadeOut, removeAction]))
        winLabel.run(SKAction.sequence([waitAction, fadeOut, removeAction]))
        levelLabel.run(SKAction.sequence([waitAction, fadeOut, removeAction]))
        nextLevelLabel.run(SKAction.sequence([waitAction, fadeOut, removeAction, cleanupAction]))
    }

    /// Create sparkle effects around victory panel
    private func createSparkleEffects(around position: CGPoint) {
        for i in 0..<12 {
            let angle = CGFloat(i) * .pi / 6 // 30 degrees apart
            let radius: CGFloat = 180
            let sparkleX = position.x + cos(angle) * radius
            let sparkleY = position.y + sin(angle) * radius

            let sparkle = SKShapeNode(circleOfRadius: 4)
            sparkle.fillColor = SKColor.yellow
            sparkle.strokeColor = SKColor.orange
            sparkle.lineWidth = 1
            sparkle.position = CGPoint(x: sparkleX, y: sparkleY)
            sparkle.zPosition = 153
            sparkle.name = "victorySparkle"
            self.addChild(sparkle)

            // Sparkle animation
            let fadeIn = SKAction.fadeIn(withDuration: 0.2)
            let scale = SKAction.scale(to: 1.5, duration: 0.3)
            let fadeOut = SKAction.fadeOut(withDuration: 0.5)
            let remove = SKAction.removeFromParent()

            let sparkleSequence = SKAction.sequence([
                SKAction.wait(forDuration: Double(i) * 0.1), // Stagger the sparkles
                SKAction.group([fadeIn, scale]),
                SKAction.wait(forDuration: 2.0),
                fadeOut,
                remove
            ])

            sparkle.run(sparkleSequence)
        }
    }

    /// Remove all victory-related elements
    private func removeVictoryElements() {
        self.children.filter { node in
            node.name?.contains("victory") == true
        }.forEach { $0.removeFromParent() }
    }

    
    /// 重置当前关卡
    func resetLevel() {
        print("重置关卡: \(currentLevelIndex + 1)")

        guard let initialState = gameStateManager.resetToInitialState() else {
            print("[GameScene] ERROR: Cannot reset - no initial state saved")
            audioManager.playSound(.error)
            return
        }

        guard let playerNode = self.player else {
            print("[GameScene] ERROR: Cannot reset - player node not found")
            return
        }

        guard boxes.count == initialState.boxPositions.count else {
            print("[GameScene] ERROR: Box count mismatch - current: \(boxes.count), initial: \(initialState.boxPositions.count)")
            return
        }

        // 重置玩家位置
        playerNode.position = scenePosition(for: initialState.playerPosition)
        print("[GameScene] Player reset to: \(initialState.playerPosition)")

        // 重置箱子位置
        for (index, boxNode) in boxes.enumerated() {
            if index < initialState.boxPositions.count {
                let boxScenePosition = scenePosition(for: initialState.boxPositions[index])
                boxNode.position = boxScenePosition
                print("[GameScene] Box \(index) reset to: \(initialState.boxPositions[index])")
            }
        }

        // 播放重置音效
        audioManager.playSound(.reset)

        print("关卡已重置到初始状态")
    }

    // Level selection menu functions removed for cleaner UI

    // UI设置函数已移至UIManager

    // 重置按钮设置函数已移至UIManager

    // 回退按钮设置函数已移至UIManager

    // Level selection button removed for cleaner UI

    // 按钮动画函数已移至UIManager

    // 音效系统已移至AudioManager

    // MARK: - 创建游戏元素方法

    /// 创建地面瓦片
    private func createGroundTile() -> SKSpriteNode {
        let groundNode = SKSpriteNode(color: SKColor(red: 0.9, green: 0.95, blue: 0.85, alpha: 1.0), size: CGSize(width: tileSize, height: tileSize))
        groundNode.name = "ground"

        // 添加细微的纹理效果
        let border = SKShapeNode(rectOf: groundNode.size, cornerRadius: 2)
        border.strokeColor = SKColor.black.withAlphaComponent(0.1)
        border.lineWidth = 1
        groundNode.addChild(border)

        return groundNode
    }

    /// 创建墙壁瓦片
    private func createWallTile() -> SKSpriteNode {
        let wallNode = SKSpriteNode(color: SKColor(red: 0.3, green: 0.2, blue: 0.1, alpha: 1.0), size: CGSize(width: tileSize, height: tileSize))
        wallNode.name = "wall"
        wallNode.zPosition = 1

        // 添加3D效果
        let highlight = SKShapeNode(rect: CGRect(x: -tileSize/2 + 2, y: -tileSize/2 + 2, width: tileSize - 4, height: tileSize - 4), cornerRadius: 3)
        highlight.fillColor = SKColor.white.withAlphaComponent(0.2)
        highlight.strokeColor = SKColor.black.withAlphaComponent(0.3)
        highlight.lineWidth = 2
        wallNode.addChild(highlight)

        return wallNode
    }

    /// 创建玩家瓦片
    private func createPlayerTile() -> SKSpriteNode {
        let playerNode = SKSpriteNode(color: .clear, size: CGSize(width: tileSize * 0.8, height: tileSize * 0.8))
        playerNode.name = "player"
        playerNode.zPosition = 3

        // 创建玩家身体（圆形）
        let body = SKShapeNode(circleOfRadius: tileSize * 0.3)
        body.fillColor = SKColor(red: 1.0, green: 0.6, blue: 0.2, alpha: 1.0) // 橙色
        body.strokeColor = SKColor(red: 0.8, green: 0.4, blue: 0.0, alpha: 1.0)
        body.lineWidth = 3
        playerNode.addChild(body)

        // 添加眼睛
        let leftEye = SKShapeNode(circleOfRadius: 3)
        leftEye.fillColor = .black
        leftEye.position = CGPoint(x: -8, y: 5)
        playerNode.addChild(leftEye)

        let rightEye = SKShapeNode(circleOfRadius: 3)
        rightEye.fillColor = .black
        rightEye.position = CGPoint(x: 8, y: 5)
        playerNode.addChild(rightEye)

        // 添加嘴巴
        let mouth = SKShapeNode(circleOfRadius: 8)
        mouth.fillColor = .clear
        mouth.strokeColor = .black
        mouth.lineWidth = 2
        mouth.position = CGPoint(x: 0, y: -5)
        playerNode.addChild(mouth)

        return playerNode
    }

    /// 创建箱子瓦片 - 改进版本，更好的3D效果
    private func createBoxTile() -> SKSpriteNode {
        let boxNode = SKSpriteNode(color: .clear, size: CGSize(width: tileSize * 0.85, height: tileSize * 0.85))
        boxNode.name = "box"
        boxNode.zPosition = 2

        // 创建主体箱子
        let mainBox = SKShapeNode(rect: CGRect(x: -tileSize * 0.4, y: -tileSize * 0.4, width: tileSize * 0.8, height: tileSize * 0.8), cornerRadius: 8)
        mainBox.fillColor = SKColor(red: 0.8, green: 0.6, blue: 0.4, alpha: 1.0) // 木箱颜色
        mainBox.strokeColor = SKColor(red: 0.6, green: 0.4, blue: 0.2, alpha: 1.0)
        mainBox.lineWidth = 3
        boxNode.addChild(mainBox)

        // 添加顶部高光效果
        let topHighlight = SKShapeNode(rect: CGRect(x: -tileSize * 0.35, y: tileSize * 0.15, width: tileSize * 0.7, height: tileSize * 0.2), cornerRadius: 4)
        topHighlight.fillColor = SKColor.white.withAlphaComponent(0.4)
        topHighlight.strokeColor = .clear
        boxNode.addChild(topHighlight)

        // 添加左侧高光效果
        let leftHighlight = SKShapeNode(rect: CGRect(x: -tileSize * 0.35, y: -tileSize * 0.35, width: tileSize * 0.15, height: tileSize * 0.7), cornerRadius: 4)
        leftHighlight.fillColor = SKColor.white.withAlphaComponent(0.25)
        leftHighlight.strokeColor = .clear
        boxNode.addChild(leftHighlight)

        // 添加右侧阴影效果
        let rightShadow = SKShapeNode(rect: CGRect(x: tileSize * 0.2, y: -tileSize * 0.35, width: tileSize * 0.15, height: tileSize * 0.7), cornerRadius: 4)
        rightShadow.fillColor = SKColor.black.withAlphaComponent(0.2)
        rightShadow.strokeColor = .clear
        boxNode.addChild(rightShadow)

        // 添加底部阴影效果
        let bottomShadow = SKShapeNode(rect: CGRect(x: -tileSize * 0.35, y: -tileSize * 0.35, width: tileSize * 0.7, height: tileSize * 0.15), cornerRadius: 4)
        bottomShadow.fillColor = SKColor.black.withAlphaComponent(0.2)
        bottomShadow.strokeColor = .clear
        boxNode.addChild(bottomShadow)

        // 添加改进的箱子标记 - 更像真实的木箱钉子
        let nailSize: CGFloat = tileSize * 0.08
        let nailPositions = [
            CGPoint(x: -tileSize * 0.25, y: tileSize * 0.25),
            CGPoint(x: tileSize * 0.25, y: tileSize * 0.25),
            CGPoint(x: -tileSize * 0.25, y: -tileSize * 0.25),
            CGPoint(x: tileSize * 0.25, y: -tileSize * 0.25)
        ]

        for position in nailPositions {
            let nail = SKShapeNode(circleOfRadius: nailSize)
            nail.fillColor = SKColor(red: 0.4, green: 0.3, blue: 0.2, alpha: 1.0)
            nail.strokeColor = SKColor.black.withAlphaComponent(0.5)
            nail.lineWidth = 1
            nail.position = position
            boxNode.addChild(nail)

            // 添加钉子的高光
            let nailHighlight = SKShapeNode(circleOfRadius: nailSize * 0.4)
            nailHighlight.fillColor = SKColor.white.withAlphaComponent(0.6)
            nailHighlight.strokeColor = .clear
            nailHighlight.position = CGPoint(x: -nailSize * 0.3, y: nailSize * 0.3)
            nail.addChild(nailHighlight)
        }

        // 添加中央的品牌标记
        let brandMark = SKLabelNode(fontNamed: "HelveticaNeue-Bold")
        brandMark.text = "📦"
        brandMark.fontSize = tileSize * 0.3
        brandMark.fontColor = SKColor.black.withAlphaComponent(0.3)
        brandMark.horizontalAlignmentMode = .center
        brandMark.verticalAlignmentMode = .center
        boxNode.addChild(brandMark)

        return boxNode
    }

    /// 创建目标瓦片
    private func createTargetTile() -> SKSpriteNode {
        let targetNode = SKSpriteNode(color: .clear, size: CGSize(width: tileSize, height: tileSize))
        targetNode.name = "target"
        targetNode.zPosition = 1

        // 创建目标圆环
        let outerCircle = SKShapeNode(circleOfRadius: tileSize * 0.35)
        outerCircle.fillColor = SKColor(red: 0.2, green: 0.8, blue: 0.2, alpha: 0.6)
        outerCircle.strokeColor = SKColor(red: 0.0, green: 0.6, blue: 0.0, alpha: 1.0)
        outerCircle.lineWidth = 3
        targetNode.addChild(outerCircle)

        let innerCircle = SKShapeNode(circleOfRadius: tileSize * 0.15)
        innerCircle.fillColor = SKColor(red: 0.0, green: 0.9, blue: 0.0, alpha: 0.8)
        innerCircle.strokeColor = .clear
        targetNode.addChild(innerCircle)

        // 添加闪烁动画
        let fadeOut = SKAction.fadeAlpha(to: 0.3, duration: 1.0)
        let fadeIn = SKAction.fadeAlpha(to: 1.0, duration: 1.0)
        let pulse = SKAction.sequence([fadeOut, fadeIn])
        let repeatPulse = SKAction.repeatForever(pulse)
        targetNode.run(repeatPulse)

        return targetNode
    }

    // MARK: - 点击移动处理

    /// 检查点击位置是否在游戏区域内
    /// - Parameter location: 点击位置
    /// - Returns: 如果在游戏区域内返回true，否则返回false
    private func isLocationInGameArea(_ location: CGPoint) -> Bool {
        let levelHeight = levelData.count
        let levelWidth = levelData.first?.count ?? 0
        let sceneWidth = CGFloat(levelWidth) * tileSize
        let sceneHeight = CGFloat(levelHeight) * tileSize

        let offsetX = (self.size.width - sceneWidth) / 2.0
        let offsetY = (self.size.height - sceneHeight) / 2.0

        let gameAreaMinX = offsetX
        let gameAreaMaxX = offsetX + sceneWidth
        let gameAreaMinY = offsetY
        let gameAreaMaxY = offsetY + sceneHeight

        return location.x >= gameAreaMinX && location.x <= gameAreaMaxX &&
               location.y >= gameAreaMinY && location.y <= gameAreaMaxY
    }

    /// 处理点击移动
    /// - Parameter location: 点击位置
    private func handleTouchMovement(to location: CGPoint) {
        guard let playerNode = self.player else { return }

        // 检查点击位置是否在游戏区域内
        guard isLocationInGameArea(location) else {
            print("[GameScene] 点击位置超出游戏区域，忽略移动")
            return
        }

        let playerGridPos = gridPosition(for: playerNode.position)
        let targetGridPos = gridPosition(for: location)

        // 计算移动方向
        let deltaX = targetGridPos.x - playerGridPos.x
        let deltaY = targetGridPos.y - playerGridPos.y

        // 确定主要移动方向
        var direction: CGVector = .zero

        if abs(deltaX) > abs(deltaY) {
            // 水平移动
            direction = CGVector(dx: deltaX > 0 ? 1 : -1, dy: 0)
        } else if abs(deltaY) > 0 {
            // 垂直移动
            direction = CGVector(dx: 0, dy: deltaY > 0 ? 1 : -1)
        }

        if direction != .zero {
            // 显示移动路径
            showMovementPath(from: playerGridPos, direction: direction)
            // 执行移动
            movePlayer(direction: direction)
        }
    }

    /// 显示移动路径
    /// - Parameters:
    ///   - startPos: 起始位置
    ///   - direction: 移动方向
    private func showMovementPath(from startPos: CGPoint, direction: CGVector) {
        // 清除之前的路径
        clearMovementPath()

        let targetPos = CGPoint(x: startPos.x + direction.dx, y: startPos.y + direction.dy)

        // 检查目标位置是否有效
        if !isWall(at: targetPos) {
            let pathNode = SKSpriteNode(color: SKColor.yellow.withAlphaComponent(0.5), size: CGSize(width: tileSize * 0.3, height: tileSize * 0.3))
            pathNode.position = scenePosition(for: targetPos)
            pathNode.zPosition = 10
            pathNode.name = "path"

            // 添加闪烁动画
            let fadeOut = SKAction.fadeAlpha(to: 0.2, duration: 0.3)
            let fadeIn = SKAction.fadeAlpha(to: 0.8, duration: 0.3)
            let blink = SKAction.sequence([fadeOut, fadeIn])
            pathNode.run(SKAction.repeatForever(blink))

            self.addChild(pathNode)
            pathNodes.append(pathNode)

            // 1秒后自动清除路径
            let wait = SKAction.wait(forDuration: 1.0)
            let clear = SKAction.run { [weak self] in
                self?.clearMovementPath()
            }
            self.run(SKAction.sequence([wait, clear]))
        }
    }

    /// 清除移动路径显示
    private func clearMovementPath() {
        for pathNode in pathNodes {
            pathNode.removeFromParent()
        }
        pathNodes.removeAll()
    }

    // TODO: 实现广告和内购相关功能
    
    // TODO: 实现更多主题场景

// MARK: - LevelManager

class LevelManager {
    private var levels: [[[Character]]] = []

    init() {
        setupDefaultLevels()
    }

    /// 设置默认的教学关卡
    private func setupDefaultLevels() {
        // 关卡 1: 简单入门 - 小网格
        levels.append([
            ["W", "W", "W", "W", "W"],
            ["W", "P", "G", "T", "W"],
            ["W", "G", "B", "G", "W"],
            ["W", "G", "G", "G", "W"],
            ["W", "W", "W", "W", "W"]
        ])

        // 关卡 2: 中等难度 - 大网格，需要策略
        levels.append([
            ["W", "W", "W", "W", "W", "W", "W", "W", "W", "W"],
            ["W", "G", "G", "G", "G", "G", "G", "G", "G", "W"],
            ["W", "G", "W", "W", "G", "G", "W", "W", "G", "W"],
            ["W", "G", "W", "T", "G", "G", "T", "W", "G", "W"],
            ["W", "G", "G", "G", "B", "B", "G", "G", "G", "W"],
            ["W", "G", "G", "G", "G", "G", "G", "G", "G", "W"],
            ["W", "G", "W", "G", "G", "P", "G", "W", "G", "W"],
            ["W", "G", "W", "T", "G", "G", "T", "W", "G", "W"],
            ["W", "G", "G", "G", "G", "G", "G", "G", "G", "W"],
            ["W", "W", "W", "W", "W", "W", "W", "W", "W", "W"]
        ])

        // 关卡 3: 复杂迷宫 - 需要精确规划
        levels.append([
            ["W", "W", "W", "W", "W", "W", "W", "W", "W", "W", "W", "W"],
            ["W", "G", "G", "G", "W", "G", "G", "G", "G", "G", "G", "W"],
            ["W", "G", "B", "G", "W", "G", "W", "W", "W", "T", "G", "W"],
            ["W", "G", "G", "G", "G", "G", "W", "G", "G", "G", "G", "W"],
            ["W", "W", "W", "G", "W", "G", "W", "G", "B", "W", "W", "W"],
            ["W", "T", "G", "G", "W", "G", "G", "G", "G", "W", "T", "W"],
            ["W", "G", "G", "P", "W", "G", "W", "G", "G", "G", "G", "W"],
            ["W", "G", "B", "G", "G", "G", "W", "G", "W", "W", "G", "W"],
            ["W", "G", "G", "G", "W", "G", "G", "G", "G", "G", "G", "W"],
            ["W", "T", "W", "W", "W", "W", "W", "W", "W", "G", "G", "W"],
            ["W", "G", "G", "G", "G", "G", "G", "G", "G", "G", "G", "W"],
            ["W", "W", "W", "W", "W", "W", "W", "W", "W", "W", "W", "W"]
        ])

        // 关卡 4: 高难度推理 - 多箱子协调
        levels.append([
            ["W", "W", "W", "W", "W", "W", "W", "W", "W", "W", "W", "W", "W", "W"],
            ["W", "G", "G", "G", "G", "G", "G", "G", "G", "G", "G", "G", "G", "W"],
            ["W", "G", "W", "W", "W", "G", "G", "G", "G", "W", "W", "W", "G", "W"],
            ["W", "G", "W", "T", "W", "G", "B", "B", "G", "W", "T", "W", "G", "W"],
            ["W", "G", "W", "G", "G", "G", "G", "G", "G", "G", "G", "W", "G", "W"],
            ["W", "G", "G", "G", "W", "W", "G", "G", "W", "W", "G", "G", "G", "W"],
            ["W", "G", "G", "G", "W", "G", "G", "P", "G", "W", "G", "G", "G", "W"],
            ["W", "G", "G", "G", "W", "W", "G", "G", "W", "W", "G", "G", "G", "W"],
            ["W", "G", "W", "G", "G", "G", "G", "G", "G", "G", "G", "W", "G", "W"],
            ["W", "G", "W", "T", "W", "G", "B", "B", "G", "W", "T", "W", "G", "W"],
            ["W", "G", "W", "W", "W", "G", "G", "G", "G", "W", "W", "W", "G", "W"],
            ["W", "G", "G", "G", "G", "G", "G", "G", "G", "G", "G", "G", "G", "W"],
            ["W", "W", "W", "W", "W", "W", "W", "W", "W", "W", "W", "W", "W", "W"]
        ])

        // 关卡 5: 终极挑战 - 大型复杂关卡
        levels.append([
            ["W", "W", "W", "W", "W", "W", "W", "W", "W", "W", "W", "W", "W", "W", "W", "W"],
            ["W", "G", "G", "G", "G", "G", "G", "G", "G", "G", "G", "G", "G", "G", "G", "W"],
            ["W", "G", "W", "W", "W", "W", "G", "G", "G", "G", "W", "W", "W", "W", "G", "W"],
            ["W", "G", "W", "T", "G", "W", "G", "B", "B", "G", "W", "G", "T", "W", "G", "W"],
            ["W", "G", "W", "G", "G", "G", "G", "G", "G", "G", "G", "G", "G", "W", "G", "W"],
            ["W", "G", "W", "G", "W", "W", "W", "G", "G", "W", "W", "W", "G", "W", "G", "W"],
            ["W", "G", "G", "G", "W", "G", "G", "G", "G", "G", "G", "W", "G", "G", "G", "W"],
            ["W", "G", "G", "B", "G", "G", "W", "G", "G", "W", "G", "G", "B", "G", "G", "W"],
            ["W", "G", "G", "G", "G", "G", "G", "G", "P", "G", "G", "G", "G", "G", "G", "W"],
            ["W", "G", "G", "B", "G", "G", "W", "G", "G", "W", "G", "G", "B", "G", "G", "W"],
            ["W", "G", "G", "G", "W", "G", "G", "G", "G", "G", "G", "W", "G", "G", "G", "W"],
            ["W", "G", "W", "G", "W", "W", "W", "G", "G", "W", "W", "W", "G", "W", "G", "W"],
            ["W", "G", "W", "G", "G", "G", "G", "G", "G", "G", "G", "G", "G", "W", "G", "W"],
            ["W", "G", "W", "T", "G", "W", "G", "B", "B", "G", "W", "G", "T", "W", "G", "W"],
            ["W", "G", "W", "W", "W", "W", "G", "G", "G", "G", "W", "W", "W", "W", "G", "W"],
            ["W", "G", "G", "G", "G", "G", "G", "G", "G", "G", "G", "G", "G", "G", "G", "W"],
            ["W", "W", "W", "W", "W", "W", "W", "W", "W", "W", "W", "W", "W", "W", "W", "W"]
        ])

        // 后续可以从文件或网络加载更多关卡
    }

    /// 获取指定索引的关卡数据
    /// - Parameter index: 关卡索引
    /// - Returns: 关卡数据 (二维字符数组)，如果索引无效则返回nil
    func getLevel(at index: Int) -> [[Character]]? {
        guard index >= 0 && index < levels.count else {
            return nil
        }
        return levels[index]
    }

    /// 获取总关卡数
    var numberOfLevels: Int {
        return levels.count
    }
    
    // TODO: 添加从文件加载关卡的功能
    // func loadLevelsFromFile(fileName: String) { ... }
}
    
    // TODO: 实现提示系统
    
    // TODO: 实现思维训练设计（黄金星星、能力报告）

}
